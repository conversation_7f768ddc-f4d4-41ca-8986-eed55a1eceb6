// Application State
let selectedServices = [];
let customerData = {};
let currentDiscount = 0; // Current discount percentage (0, 5, 10, 15)

// Check if running in Electron
const isElectron = typeof window !== 'undefined' && window.process && window.process.type;

// Electron specific functionality
if (isElectron) {
    const { ipc<PERSON>enderer } = require('electron');

    // Enhanced save/load functionality for Electron
    window.electronAPI = {
        saveFile: async (data, filename) => {
            const result = await ipcRenderer.invoke('show-save-dialog', {
                defaultPath: filename,
                filters: [
                    { name: 'JSON súbory', extensions: ['json'] },
                    { name: '<PERSON><PERSON><PERSON><PERSON> súbory', extensions: ['*'] }
                ]
            });

            if (!result.canceled) {
                const writeResult = await ipcRenderer.invoke('write-file', result.filePath, data);
                return writeResult;
            }
            return { success: false, canceled: true };
        },

        openFile: async () => {
            const result = await ipc<PERSON>enderer.invoke('show-open-dialog', {
                properties: ['openFile'],
                filters: [
                    { name: 'JSON súbory', extensions: ['json'] },
                    { name: '<PERSON><PERSON><PERSON><PERSON> súbory', extensions: ['*'] }
                ]
            });

            if (!result.canceled && result.filePaths.length > 0) {
                const readResult = await ipcRenderer.invoke('read-file', result.filePaths[0]);
                return readResult;
            }
            return { success: false, canceled: true };
        }
    };
}

// DOM Elements (will be initialized after DOM loads)
let serviceCheckboxes;
let customerForm;
let selectedServicesList;
let subtotalElement;
let vatElement;
let totalElement;
let generatePDFButton;
let discountElements;
let discountRowElement;
let discountPercentElement;
let discountAmountElement;
let subtotalAfterDiscountElement;

// Initialize Application
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DOM elements
    serviceCheckboxes = document.querySelectorAll('input[type="checkbox"][data-service]');
    customerForm = document.querySelector('.customer-section');
    selectedServicesList = document.getElementById('selectedServicesList');
    subtotalElement = document.getElementById('subtotal');
    vatElement = document.getElementById('vat');
    totalElement = document.getElementById('total');
    generatePDFButton = document.querySelector('.btn-generate');

    // Discount elements
    discountElements = document.querySelectorAll('input[name="discount"]');
    discountRowElement = document.getElementById('discountRow');
    discountPercentElement = document.getElementById('discountPercent');
    discountAmountElement = document.getElementById('discountAmount');
    subtotalAfterDiscountElement = document.getElementById('subtotalAfterDiscount');

    initializeEventListeners();
    updateCalculation();
    initializeNavigation();

    // Check if html2pdf is loaded
    setTimeout(checkHtml2PdfAvailability, 1000);

    // Initialize tab system
    initializeTabSystem();
});

function checkHtml2PdfAvailability() {
    console.log('Checking html2pdf availability...');
    console.log('generatePDFButton:', generatePDFButton);

    if (typeof window.html2pdf === 'undefined') {
        console.warn('html2pdf is not available, PDF generation will not work');
        if (generatePDFButton) {
            generatePDFButton.title = 'html2pdf knižnica nie je dostupná';
            generatePDFButton.style.opacity = '0.5';
        }
    } else {
        console.log('html2pdf is available and ready');
        if (generatePDFButton) {
            generatePDFButton.title = 'Generovať PDF ponuku';
            generatePDFButton.style.opacity = '1';
        }
    }
}

function initializeTabSystem() {
    // Set default active tab to dashboard
    const defaultTab = 'dashboard';
    switchTab(defaultTab);

    // Initialize dashboard button listeners
    initializeDashboardButtons();

    // Initialize mobile navigation
    initializeMobileNavigation();

    // Initialize animations
    initializeAnimations();

    // Initialize animated stats
    animateStatsOnLoad();

    // Initialize performance optimizations
    initializePerformanceOptimizations();

    // Start performance monitoring
    measurePerformance();

    // Initialize responsive testing
    initializeResponsiveTesting();

    // Validate responsive design
    setTimeout(() => {
        validateResponsiveDesign();
    }, 1000);

    // Add testing keyboard shortcuts
    addTestingKeyboardShortcuts();

    // Initialize Firebase integration
    initializeFirebaseIntegration();

    // Close modals when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.classList.remove('show');
            event.target.style.display = 'none';
        }
    });
}

// Dashboard functionality
function initializeDashboardButtons() {
    const dashboardButtons = document.querySelectorAll('.dashboard-button');
    dashboardButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');
            if (tabName) {
                switchTab(tabName);
            }
        });
    });

    // Update dashboard stats
    updateDashboardStats();
}

async function updateDashboardStats() {
    try {
        // Show loading state
        showStatsLoading();

        // Get data from Firebase or fallback to local data
        let customers = [];
        let invoices = [];
        let payments = [];

        if (window.firebaseService) {
            try {
                customers = await window.firebaseService.getCustomers();
                payments = await window.firebaseService.getPayments();

                // For invoices, use local data if Firebase doesn't have them
                if (typeof window.invoices !== 'undefined') {
                    invoices = window.invoices;
                }
            } catch (error) {
                console.warn('Firebase data fetch failed, using local data:', error);
                // Fallback to local data
                customers = typeof window.clients !== 'undefined' ? window.clients : [];
                invoices = typeof window.invoices !== 'undefined' ? window.invoices : [];
            }
        } else {
            // Use local data if Firebase is not available
            customers = typeof window.clients !== 'undefined' ? window.clients : [];
            invoices = typeof window.invoices !== 'undefined' ? window.invoices : [];
        }

        // Update client count
        const totalClientsElement = document.getElementById('totalClientsCount');
        if (totalClientsElement) {
            totalClientsElement.dataset.target = customers.length;
            animateCounter(totalClientsElement, customers.length);
        }

        // Update invoices count
        const totalInvoicesElement = document.getElementById('totalInvoicesCount');
        if (totalInvoicesElement) {
            totalInvoicesElement.dataset.target = invoices.length;
            animateCounter(totalInvoicesElement, invoices.length);
        }

        // Update revenue
        const totalRevenueElement = document.getElementById('totalRevenueCount');
        if (totalRevenueElement) {
            const totalRevenue = invoices.reduce((sum, invoice) => sum + (invoice.total || 0), 0);
            totalRevenueElement.dataset.target = totalRevenue;

            // Animate revenue counter
            const start = parseInt(totalRevenueElement.textContent) || 0;
            const increment = (totalRevenue - start) / 60;
            let current = start;

            const timer = setInterval(() => {
                current += increment;
                if ((increment > 0 && current >= totalRevenue) || (increment < 0 && current <= totalRevenue)) {
                    current = totalRevenue;
                    clearInterval(timer);
                }
                totalRevenueElement.textContent = `${Math.floor(current)}€`;
            }, 16);
        }

        // Hide loading state
        hideStatsLoading();

    } catch (error) {
        console.error('Error updating dashboard stats:', error);
        hideStatsLoading();
    }
}

function showStatsLoading() {
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.classList.add('loading');
    });
}

function hideStatsLoading() {
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.classList.remove('loading');
    });
}

// Mobile navigation functions
function initializeMobileNavigation() {
    // Mobile menu items
    const mobileMenuItems = document.querySelectorAll('.mobile-menu-item');
    mobileMenuItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const tabName = this.getAttribute('data-tab');
            if (tabName) {
                switchTab(tabName);
                closeMobileMenu();
            }
        });
    });

    // Add swipe gesture support
    initializeSwipeGestures();

    // Close menu on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeMobileMenu();
        }
    });

    // Prevent body scroll when menu is open
    const overlay = document.querySelector('.mobile-menu-overlay');
    if (overlay) {
        overlay.addEventListener('touchmove', function(e) {
            e.preventDefault();
        }, { passive: false });
    }
}

function initializeSwipeGestures() {
    let startX = 0;
    let startY = 0;
    let isMenuOpen = false;

    document.addEventListener('touchstart', function(e) {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
        isMenuOpen = document.querySelector('.mobile-menu-overlay').classList.contains('show');
    });

    document.addEventListener('touchend', function(e) {
        const endX = e.changedTouches[0].clientX;
        const endY = e.changedTouches[0].clientY;
        const diffX = startX - endX;
        const diffY = Math.abs(startY - endY);

        // Only trigger if horizontal swipe is more significant than vertical
        if (Math.abs(diffX) > diffY && Math.abs(diffX) > 50) {
            // Swipe left to close menu (when menu is open)
            if (diffX > 0 && isMenuOpen) {
                closeMobileMenu();
            }
            // Swipe right to open menu (from right edge)
            else if (diffX < 0 && startX > window.innerWidth - 50 && !isMenuOpen) {
                toggleMobileMenu();
            }
        }
    });
}

function toggleMobileMenu() {
    const overlay = document.querySelector('.mobile-menu-overlay');
    const menu = document.querySelector('.mobile-menu');
    const menuButton = document.querySelector('.menu-button');
    const body = document.body;

    if (overlay && menu) {
        const isOpen = overlay.classList.contains('show');

        if (isOpen) {
            overlay.classList.remove('show');
            menu.classList.remove('show');
            menu.classList.remove('open');
            menuButton.classList.remove('active');
            body.classList.remove('mobile-menu-open');
        } else {
            overlay.classList.add('show');
            menu.classList.add('show');
            menu.classList.add('open');
            menuButton.classList.add('active');
            body.classList.add('mobile-menu-open');
        }
    }
}

function closeMobileMenu() {
    const overlay = document.querySelector('.mobile-menu-overlay');
    const menu = document.querySelector('.mobile-menu');
    const menuButton = document.querySelector('.menu-button');
    const body = document.body;

    if (overlay && menu) {
        overlay.classList.remove('show');
        menu.classList.remove('show');
        menu.classList.remove('open');
        body.classList.remove('mobile-menu-open');
        if (menuButton) {
            menuButton.classList.remove('active');
        }
    }
}

function goToDashboard() {
    switchTab('dashboard');
}

function updateCurrentSectionTitle(tabName) {
    const titleElement = document.querySelector('.current-section-title');
    const backButton = document.querySelector('.back-button');

    if (titleElement) {
        const titles = {
            'dashboard': 'Dashboard',
            'quotes': 'Cenová ponuka',
            'invoices': 'Faktúry',
            'orders': 'Objednávky',
            'clients': 'Klienti',
            'services': 'Služby',
            'settings': 'Nastavenia',
            'email': 'Email'
        };

        titleElement.textContent = titles[tabName] || 'Dashboard';

        // Show/hide back button
        if (backButton) {
            if (tabName === 'dashboard') {
                backButton.style.display = 'none';
            } else {
                backButton.style.display = 'block';
            }
        }
    }
}

// Animation and Interaction Enhancements
function initializeAnimations() {
    // Add stagger animation to service items
    const serviceItems = document.querySelectorAll('.service-item');
    serviceItems.forEach((item, index) => {
        item.style.animationDelay = `${index * 0.1}s`;
        item.classList.add('fade-in');
    });

    // Add stagger animation to stat cards
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('slide-in-right');
    });

    // Add intersection observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe elements for scroll animations
    const animatedElements = document.querySelectorAll('.dashboard-section, .email-templates, .clients-stats');
    animatedElements.forEach(el => observer.observe(el));

    // Add ripple effect to buttons
    addRippleEffect();

    // Add smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';
}

function addRippleEffect() {
    const buttons = document.querySelectorAll('.btn, .dashboard-button, .template-card');

    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

function animateCounter(element, target, duration = 1000) {
    const start = parseInt(element.textContent) || 0;
    const increment = (target - start) / (duration / 16);
    let current = start;

    const timer = setInterval(() => {
        current += increment;
        if ((increment > 0 && current >= target) || (increment < 0 && current <= target)) {
            current = target;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current);
    }, 16);
}

function animateStatsOnLoad() {
    // Animate dashboard stats when they become visible
    const statsElements = [
        { id: 'totalClientsCount', target: 0 },
        { id: 'totalInvoicesCount', target: 0 },
        { id: 'totalRevenueCount', target: 0 }
    ];

    statsElements.forEach(stat => {
        const element = document.getElementById(stat.id);
        if (element) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const targetValue = parseInt(element.dataset.target) || stat.target;
                        animateCounter(element, targetValue);
                        observer.unobserve(element);
                    }
                });
            });
            observer.observe(element);
        }
    });
}

// Mobile Performance Optimizations
function initializePerformanceOptimizations() {
    // Detect touch device
    if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
        document.body.classList.add('touch-device');
    }

    // Implement lazy loading for images
    implementLazyLoading();

    // Optimize scroll performance
    optimizeScrollPerformance();

    // Preload critical resources
    preloadCriticalResources();

    // Initialize loading states
    initializeLoadingStates();

    // Optimize touch interactions
    optimizeTouchInteractions();
}

function implementLazyLoading() {
    const images = document.querySelectorAll('img[loading="lazy"]');

    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.classList.add('lazy-load');

                    img.onload = () => {
                        img.classList.add('loaded');
                    };

                    observer.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    }
}

function optimizeScrollPerformance() {
    let ticking = false;

    function updateScrollPosition() {
        // Optimize scroll-based animations
        const scrollTop = window.pageYOffset;

        // Update navigation state based on scroll
        if (scrollTop > 100) {
            document.body.classList.add('scrolled');
        } else {
            document.body.classList.remove('scrolled');
        }

        ticking = false;
    }

    window.addEventListener('scroll', () => {
        if (!ticking) {
            requestAnimationFrame(updateScrollPosition);
            ticking = true;
        }
    }, { passive: true });
}

function preloadCriticalResources() {
    // Preload critical CSS and fonts
    const criticalResources = [
        'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap',
        'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'
    ];

    criticalResources.forEach(resource => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'style';
        link.href = resource;
        document.head.appendChild(link);
    });
}

function initializeLoadingStates() {
    // Show loading skeletons while content loads
    const loadingElements = document.querySelectorAll('[data-loading]');

    loadingElements.forEach(element => {
        showLoadingSkeleton(element);

        // Simulate loading completion (replace with actual loading logic)
        setTimeout(() => {
            hideLoadingSkeleton(element);
        }, 1000);
    });
}

function showLoadingSkeleton(element) {
    const skeleton = createSkeletonElement(element);
    element.style.display = 'none';
    element.parentNode.insertBefore(skeleton, element);
}

function hideLoadingSkeleton(element) {
    const skeleton = element.parentNode.querySelector('.skeleton-container');
    if (skeleton) {
        skeleton.remove();
    }
    element.style.display = '';
    element.classList.add('fade-in');
}

function createSkeletonElement(element) {
    const skeleton = document.createElement('div');
    skeleton.className = 'skeleton-container';

    if (element.classList.contains('dashboard-button')) {
        skeleton.innerHTML = `
            <div class="skeleton skeleton-card"></div>
        `;
    } else if (element.classList.contains('stat-card')) {
        skeleton.innerHTML = `
            <div class="skeleton skeleton-text large"></div>
            <div class="skeleton skeleton-text"></div>
        `;
    } else {
        skeleton.innerHTML = `
            <div class="skeleton skeleton-text"></div>
            <div class="skeleton skeleton-text"></div>
            <div class="skeleton skeleton-text small"></div>
        `;
    }

    return skeleton;
}

function optimizeTouchInteractions() {
    // Improve touch responsiveness
    const touchElements = document.querySelectorAll('.dashboard-button, .btn, .template-card, .service-label');

    touchElements.forEach(element => {
        element.addEventListener('touchstart', function() {
            this.classList.add('touch-active');
        }, { passive: true });

        element.addEventListener('touchend', function() {
            setTimeout(() => {
                this.classList.remove('touch-active');
            }, 150);
        }, { passive: true });

        element.addEventListener('touchcancel', function() {
            this.classList.remove('touch-active');
        }, { passive: true });
    });
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Performance monitoring
function measurePerformance() {
    if ('performance' in window) {
        window.addEventListener('load', () => {
            const perfData = performance.getEntriesByType('navigation')[0];
            console.log('Page Load Time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
            console.log('DOM Content Loaded:', perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart, 'ms');
        });
    }
}

// Responsive Design Testing
function initializeResponsiveTesting() {
    // Add viewport size indicator for testing
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        addViewportIndicator();
    }

    // Test responsive breakpoints
    testResponsiveBreakpoints();

    // Monitor orientation changes
    window.addEventListener('orientationchange', handleOrientationChange);
    window.addEventListener('resize', debounce(handleResize, 250));
}

function addViewportIndicator() {
    const indicator = document.createElement('div');
    indicator.id = 'viewport-indicator';
    indicator.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 9999;
        font-family: monospace;
    `;
    document.body.appendChild(indicator);

    updateViewportIndicator();
}

function updateViewportIndicator() {
    const indicator = document.getElementById('viewport-indicator');
    if (indicator) {
        const width = window.innerWidth;
        const height = window.innerHeight;
        let breakpoint = 'XL';

        if (width < 480) breakpoint = 'XS';
        else if (width < 768) breakpoint = 'SM';
        else if (width < 1024) breakpoint = 'MD';
        else if (width < 1200) breakpoint = 'LG';

        indicator.textContent = `${width}×${height} (${breakpoint})`;
    }
}

function testResponsiveBreakpoints() {
    const breakpoints = {
        mobile: 768,
        tablet: 1024,
        desktop: 1200
    };

    const currentWidth = window.innerWidth;

    // Apply appropriate classes based on screen size
    document.body.classList.remove('mobile', 'tablet', 'desktop');

    if (currentWidth < breakpoints.mobile) {
        document.body.classList.add('mobile');
        enableMobileOptimizations();
    } else if (currentWidth < breakpoints.tablet) {
        document.body.classList.add('tablet');
    } else {
        document.body.classList.add('desktop');
    }
}

function enableMobileOptimizations() {
    // Enable mobile-specific optimizations
    document.body.classList.add('mobile-optimized');

    // Disable hover effects on mobile
    const hoverElements = document.querySelectorAll('.dashboard-button, .template-card, .stat-card, .service-label');
    hoverElements.forEach(el => {
        el.classList.add('mobile-no-hover');
    });

    // Add touch feedback for mobile elements
    const touchElements = document.querySelectorAll('.dashboard-button, .service-label, .btn');
    touchElements.forEach(el => {
        el.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.98)';
        });

        el.addEventListener('touchend', function() {
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });

    // Optimize service category display for mobile
    optimizeServiceCategoriesForMobile();

    // Add mobile-specific viewport handling
    handleMobileViewport();
}

function optimizeServiceCategoriesForMobile() {
    const serviceCategories = document.querySelectorAll('.service-category');
    serviceCategories.forEach(category => {
        const subcategories = category.querySelectorAll('.service-subcategory');

        // Add mobile-friendly spacing
        subcategories.forEach((subcategory, index) => {
            subcategory.style.marginBottom = '1rem';

            // Add collapse/expand functionality for mobile
            const header = subcategory.querySelector('h4');
            if (header && subcategory.children.length > 2) {
                header.style.cursor = 'pointer';
                header.addEventListener('click', () => {
                    const items = subcategory.querySelectorAll('.service-item');
                    const isCollapsed = subcategory.classList.contains('collapsed');

                    if (isCollapsed) {
                        subcategory.classList.remove('collapsed');
                        items.forEach(item => item.style.display = 'block');
                        header.innerHTML = header.innerHTML.replace('▶', '▼');
                    } else {
                        subcategory.classList.add('collapsed');
                        items.forEach(item => item.style.display = 'none');
                        header.innerHTML = header.innerHTML.replace('▼', '▶');
                    }
                });
            }
        });
    });
}

function handleMobileViewport() {
    // Prevent zoom on input focus for iOS
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            if (window.innerWidth <= 768) {
                const viewport = document.querySelector('meta[name="viewport"]');
                if (viewport) {
                    viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
                }
            }
        });

        input.addEventListener('blur', function() {
            if (window.innerWidth <= 768) {
                const viewport = document.querySelector('meta[name="viewport"]');
                if (viewport) {
                    viewport.setAttribute('content', 'width=device-width, initial-scale=1.0');
                }
            }
        });
    });
}

function handleOrientationChange() {
    setTimeout(() => {
        testResponsiveBreakpoints();
        updateViewportIndicator();

        // Recalculate dashboard grid
        const dashboardGrid = document.querySelector('.dashboard-grid');
        if (dashboardGrid) {
            dashboardGrid.style.display = 'none';
            setTimeout(() => {
                dashboardGrid.style.display = 'grid';
            }, 100);
        }
    }, 100);
}

function handleResize() {
    testResponsiveBreakpoints();
    updateViewportIndicator();

    // Adjust mobile menu if open
    const mobileMenu = document.querySelector('.mobile-menu');
    if (mobileMenu && mobileMenu.classList.contains('open')) {
        if (window.innerWidth > 768) {
            closeMobileMenu();
        }
    }
}

function validateResponsiveDesign() {
    const issues = [];

    // Check if dashboard buttons are properly sized
    const dashboardButtons = document.querySelectorAll('.dashboard-button');
    dashboardButtons.forEach((button, index) => {
        const rect = button.getBoundingClientRect();
        if (rect.width < 44 || rect.height < 44) {
            issues.push(`Dashboard button ${index + 1} is too small for touch (${rect.width}×${rect.height})`);
        }
    });

    // Check if text is readable
    const textElements = document.querySelectorAll('p, span, label');
    textElements.forEach((el, index) => {
        const styles = window.getComputedStyle(el);
        const fontSize = parseFloat(styles.fontSize);
        if (fontSize < 14) {
            issues.push(`Text element ${index + 1} has font size too small: ${fontSize}px`);
        }
    });

    // Check for horizontal overflow
    if (document.body.scrollWidth > window.innerWidth) {
        issues.push('Horizontal overflow detected');
    }

    if (issues.length > 0) {
        console.warn('Responsive design issues found:', issues);
    } else {
        console.log('Responsive design validation passed');
    }

    return issues;
}

// Test different screen sizes
function testScreenSizes() {
    const testSizes = [
        { name: 'iPhone SE', width: 375, height: 667 },
        { name: 'iPhone 12', width: 390, height: 844 },
        { name: 'iPad', width: 768, height: 1024 },
        { name: 'iPad Pro', width: 1024, height: 1366 },
        { name: 'Desktop', width: 1920, height: 1080 }
    ];

    console.log('Testing responsive design at different screen sizes:');

    testSizes.forEach(size => {
        // Simulate viewport size
        const originalWidth = window.innerWidth;
        const originalHeight = window.innerHeight;

        // This is just for logging - actual testing would need browser dev tools
        console.log(`${size.name} (${size.width}×${size.height}):`);

        // Test dashboard grid layout
        const dashboardGrid = document.querySelector('.dashboard-grid');
        if (dashboardGrid) {
            const computedStyle = window.getComputedStyle(dashboardGrid);
            console.log(`  Grid columns: ${computedStyle.gridTemplateColumns}`);
        }

        // Test navigation visibility
        const mobileNav = document.querySelector('.mobile-nav-header');
        const desktopNav = document.querySelector('.desktop-nav');
        const bottomNav = document.querySelector('.pwa-bottom-nav');

        if (size.width < 768) {
            console.log('  Mobile layout expected');
        } else if (size.width < 1024) {
            console.log('  Tablet layout expected');
        } else {
            console.log('  Desktop layout expected');
        }
    });
}

// Add keyboard shortcuts for testing
function addTestingKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Only in development mode
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 't':
                        e.preventDefault();
                        testScreenSizes();
                        break;
                    case 'r':
                        e.preventDefault();
                        validateResponsiveDesign();
                        break;
                    case 'v':
                        e.preventDefault();
                        const indicator = document.getElementById('viewport-indicator');
                        if (indicator) {
                            indicator.style.display = indicator.style.display === 'none' ? 'block' : 'none';
                        }
                        break;
                }
            }
        }
    });
}

// Email functionality with Firebase integration
function composeEmail() {
    showEmailComposer();
}

function showEmailComposer() {
    // Create email composer modal
    const modal = document.createElement('div');
    modal.className = 'modal email-composer-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-envelope"></i> Nový email</h3>
                <button class="close-modal" onclick="closeEmailComposer()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="emailForm">
                    <div class="form-group">
                        <label for="emailTo">Príjemca:</label>
                        <input type="email" id="emailTo" required>
                    </div>
                    <div class="form-group">
                        <label for="emailSubject">Predmet:</label>
                        <input type="text" id="emailSubject" required>
                    </div>
                    <div class="form-group">
                        <label for="emailBody">Správa:</label>
                        <textarea id="emailBody" rows="8" required></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeEmailComposer()">Zrušiť</button>
                        <button type="submit" class="btn btn-primary">Odoslať</button>
                    </div>
                </form>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    modal.style.display = 'flex';

    // Handle form submission
    document.getElementById('emailForm').addEventListener('submit', handleEmailSubmit);
}

function closeEmailComposer() {
    const modal = document.querySelector('.email-composer-modal');
    if (modal) {
        modal.remove();
    }
}

async function handleEmailSubmit(e) {
    e.preventDefault();

    const formData = {
        to: document.getElementById('emailTo').value,
        subject: document.getElementById('emailSubject').value,
        body: document.getElementById('emailBody').value,
        sentAt: new Date(),
        status: 'sent'
    };

    try {
        // Save email to Firebase
        if (window.firebaseService) {
            await saveEmailToFirebase(formData);
        }

        // Save to local storage as backup
        saveEmailToLocal(formData);

        // Update email history
        updateEmailHistory();

        // Show success message
        showNotification('Email bol úspešne odoslaný!', 'success');

        closeEmailComposer();

    } catch (error) {
        console.error('Error sending email:', error);
        showNotification('Chyba pri odosielaní emailu. Email bol uložený pre neskoršie odoslanie.', 'warning');

        // Save for later sending
        formData.status = 'pending';
        saveEmailToLocal(formData);
        closeEmailComposer();
    }
}

async function saveEmailToFirebase(emailData) {
    if (window.firebaseService) {
        // Add to emails collection
        const emailsRef = window.firebaseService.db ?
            window.firebaseService.db.collection('emails') : null;

        if (emailsRef) {
            await emailsRef.add(emailData);
        }
    }
}

function saveEmailToLocal(emailData) {
    const emails = JSON.parse(localStorage.getItem('emails') || '[]');
    emails.push({ id: Date.now(), ...emailData });
    localStorage.setItem('emails', JSON.stringify(emails));
}

function updateEmailHistory() {
    const emails = JSON.parse(localStorage.getItem('emails') || '[]');
    const emailList = document.getElementById('emailHistoryList');

    if (emailList) {
        if (emails.length === 0) {
            emailList.innerHTML = '<p class="no-emails">Žiadne odoslané emaily</p>';
        } else {
            emailList.innerHTML = emails.map(email => `
                <div class="email-item">
                    <div class="email-header">
                        <strong>${email.subject}</strong>
                        <span class="email-date">${new Date(email.sentAt).toLocaleDateString()}</span>
                    </div>
                    <div class="email-recipient">Pre: ${email.to}</div>
                    <div class="email-status ${email.status}">${email.status === 'sent' ? 'Odoslané' : 'Čaká na odoslanie'}</div>
                </div>
            `).join('');
        }
    }
}

function useTemplate(templateType) {
    const templates = {
        'quote': {
            subject: 'Cenová ponuka - eHroby',
            body: 'Dobrý deň,\n\nv prílohe Vám posielame cenovú ponuku na naše služby.\n\nS pozdravom,\neHroby tím'
        },
        'invoice': {
            subject: 'Faktúra - eHroby',
            body: 'Dobrý deň,\n\nv prílohe Vám posielame faktúru za poskytnuté služby.\n\nS pozdravom,\neHroby tím'
        },
        'reminder': {
            subject: 'Pripomienka platby - eHroby',
            body: 'Dobrý deň,\n\nupozorňujeme Vás na nezaplatenú faktúru.\n\nS pozdravom,\neHroby tím'
        },
        'thank-you': {
            subject: 'Poďakovanie - eHroby',
            body: 'Dobrý deň,\n\nďakujeme Vám za dôveru a spoluprácu.\n\nS pozdravom,\neHroby tím'
        }
    };

    const template = templates[templateType];
    if (template) {
        showEmailComposer();

        // Pre-fill the form with template data
        setTimeout(() => {
            const subjectField = document.getElementById('emailSubject');
            const bodyField = document.getElementById('emailBody');

            if (subjectField) subjectField.value = template.subject;
            if (bodyField) bodyField.value = template.body;
        }, 100);
    }
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()">&times;</button>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Firebase Integration
function initializeFirebaseIntegration() {
    // Add Firebase status indicator
    addFirebaseStatusIndicator();

    // Setup Firebase listeners
    setupFirebaseListeners();

    // Initialize email history
    updateEmailHistory();

    // Setup offline sync
    setupOfflineSync();
}

function addFirebaseStatusIndicator() {
    const indicator = document.createElement('div');
    indicator.id = 'firebase-status';
    indicator.className = 'firebase-status';
    indicator.textContent = 'Connecting...';
    document.body.appendChild(indicator);

    updateFirebaseStatus();
}

function updateFirebaseStatus() {
    const indicator = document.getElementById('firebase-status');
    if (!indicator) return;

    if (navigator.onLine) {
        if (window.firebaseService) {
            indicator.textContent = 'Online';
            indicator.className = 'firebase-status online';
            indicator.style.display = 'block';

            // Hide after 3 seconds if online
            setTimeout(() => {
                indicator.style.display = 'none';
            }, 3000);
        } else {
            indicator.textContent = 'Firebase not available';
            indicator.className = 'firebase-status offline';
            indicator.style.display = 'block';
        }
    } else {
        indicator.textContent = 'Offline';
        indicator.className = 'firebase-status offline';
        indicator.style.display = 'block';
    }
}

function setupFirebaseListeners() {
    // Listen for online/offline events
    window.addEventListener('online', () => {
        updateFirebaseStatus();
        syncOfflineData();
    });

    window.addEventListener('offline', () => {
        updateFirebaseStatus();
    });

    // Setup real-time listeners if Firebase is available
    if (window.firebaseService) {
        try {
            // Listen for customer changes
            window.firebaseService.onCustomersChange((customers) => {
                window.clients = customers;
                updateDashboardStats();

                // Update clients display if on clients tab
                if (typeof updateClientsDisplay === 'function') {
                    updateClientsDisplay();
                }
            });

            // Listen for task changes
            window.firebaseService.onTasksChange((tasks) => {
                window.tasks = tasks;

                // Update orders display if on orders tab
                if (typeof renderTasks === 'function') {
                    renderTasks();
                }
            });

        } catch (error) {
            console.warn('Firebase listeners setup failed:', error);
        }
    }
}

function setupOfflineSync() {
    // Sync data when coming back online
    if (window.firebaseService) {
        window.addEventListener('online', () => {
            const indicator = document.getElementById('firebase-status');
            if (indicator) {
                indicator.textContent = 'Syncing...';
                indicator.className = 'firebase-status syncing';
                indicator.style.display = 'block';
            }

            // Sync offline data
            window.firebaseService.syncOfflineData().then(() => {
                updateFirebaseStatus();
                updateDashboardStats();
                showNotification('Data synchronized successfully', 'success');
            }).catch(error => {
                console.error('Sync failed:', error);
                showNotification('Sync failed, will retry later', 'warning');
            });
        });
    }
}

async function syncOfflineData() {
    if (window.firebaseService && navigator.onLine) {
        try {
            await window.firebaseService.syncOfflineData();
            console.log('Offline data synced successfully');
        } catch (error) {
            console.error('Failed to sync offline data:', error);
        }
    }
}

// Enhanced data management with Firebase
async function saveDataWithFirebase(collection, data) {
    try {
        if (window.firebaseService && navigator.onLine) {
            // Save to Firebase
            let result;
            switch(collection) {
                case 'customers':
                    result = await window.firebaseService.addCustomer(data);
                    break;
                case 'tasks':
                    result = await window.firebaseService.addTask(data);
                    break;
                case 'payments':
                    result = await window.firebaseService.addPayment(data);
                    break;
                default:
                    throw new Error('Unknown collection');
            }

            showNotification('Data saved successfully', 'success');
            return result;
        } else {
            // Save offline
            const localData = JSON.parse(localStorage.getItem(collection) || '[]');
            const newItem = { id: 'offline_' + Date.now(), ...data };
            localData.push(newItem);
            localStorage.setItem(collection, JSON.stringify(localData));

            showNotification('Data saved offline, will sync when online', 'info');
            return newItem.id;
        }
    } catch (error) {
        console.error('Error saving data:', error);
        showNotification('Error saving data', 'error');
        throw error;
    }
}

async function updateDataWithFirebase(collection, id, updates) {
    try {
        if (window.firebaseService && navigator.onLine) {
            // Update in Firebase
            let result;
            switch(collection) {
                case 'customers':
                    result = await window.firebaseService.updateCustomer(id, updates);
                    break;
                case 'tasks':
                    result = await window.firebaseService.updateTask(id, updates);
                    break;
                case 'payments':
                    result = await window.firebaseService.updatePayment(id, updates);
                    break;
                default:
                    throw new Error('Unknown collection');
            }

            showNotification('Data updated successfully', 'success');
            return result;
        } else {
            // Update offline
            const localData = JSON.parse(localStorage.getItem(collection) || '[]');
            const index = localData.findIndex(item => item.id === id);
            if (index !== -1) {
                localData[index] = { ...localData[index], ...updates };
                localStorage.setItem(collection, JSON.stringify(localData));

                showNotification('Data updated offline, will sync when online', 'info');
                return true;
            }
            return false;
        }
    } catch (error) {
        console.error('Error updating data:', error);
        showNotification('Error updating data', 'error');
        throw error;
    }
}

// Tab switching function
function switchTab(tabName) {
    // Update nav tabs
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    const activeTab = document.querySelector(`[data-tab="${tabName}"]`);
    if (activeTab) {
        activeTab.classList.add('active');
    }

    // Update tab panes
    document.querySelectorAll('.tab-pane').forEach(pane => {
        pane.classList.remove('active');
    });
    const activePane = document.getElementById(`${tabName}-tab`);
    if (activePane) {
        activePane.classList.add('active');
    }

    // Update mobile navigation
    updateCurrentSectionTitle(tabName);

    // Update bottom navigation
    document.querySelectorAll('.pwa-bottom-nav .nav-item').forEach(item => {
        item.classList.remove('active');
    });
    const activeBottomNav = document.querySelector(`.pwa-bottom-nav [data-tab="${tabName}"]`);
    if (activeBottomNav) {
        activeBottomNav.classList.add('active');
    }

    // Update mobile menu items
    document.querySelectorAll('.mobile-menu-item').forEach(item => {
        item.classList.remove('active');
    });
    const activeMobileItem = document.querySelector(`.mobile-menu-item[data-tab="${tabName}"]`);
    if (activeMobileItem) {
        activeMobileItem.classList.add('active');
    }

    // Update content based on active tab
    switch(tabName) {
        case 'dashboard':
            updateDashboardStats();
            break;
        case 'orders':
            if (typeof updateDashboard === 'function') {
                updateDashboard();
            }
            if (typeof renderTasks === 'function') {
                renderTasks();
            }
            break;
        case 'clients':
            if (typeof updateClientsDisplay === 'function') {
                updateClientsDisplay();
                updateCRMStats();
            }
            break;
        case 'invoices':
            if (typeof updateInvoicesDisplay === 'function') {
                updateInvoicesDisplay();
                updateCRMStats();
            }
            break;
        case 'services':
            if (typeof updateServicesDisplay === 'function') {
                updateServicesDisplay();
            }
            break;
        case 'email':
            // Email tab specific initialization if needed
            break;
    }
}

// Event Listeners
function initializeEventListeners() {
    // Service selection
    if (serviceCheckboxes) {
        serviceCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', handleServiceChange);
        });
    }

    // Custom input fields
    document.addEventListener('input', handleCustomInputChange);

    // Customer form
    if (customerForm) {
        const customerInputs = customerForm.querySelectorAll('input');
        customerInputs.forEach(input => {
            input.addEventListener('input', updateCustomerData);
        });
    }

    // Generate PDF button
    if (generatePDFButton) {
        generatePDFButton.addEventListener('click', generatePDF);
    }

    // Discount selection
    if (discountElements) {
        discountElements.forEach(radio => {
            radio.addEventListener('change', handleDiscountChange);
        });
    }

    // Navigation
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', handleNavigation);
    });

    // Main navigation tabs
    const navTabs = document.querySelectorAll('.nav-tab');
    navTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const tabName = this.dataset.tab;
            if (tabName) {
                switchTab(tabName);
            }
        });
    });

    // Bottom navigation items
    const bottomNavItems = document.querySelectorAll('.pwa-bottom-nav .nav-item');
    bottomNavItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const tabName = this.dataset.tab;
            if (tabName) {
                switchTab(tabName);
            }
        });
    });
}

// Navigation
function initializeNavigation() {
    // Categories are handled by CSS classes now
}

function handleNavigation(e) {
    e.preventDefault();
    const targetId = e.currentTarget.getAttribute('href').substring(1);

    // Update active nav link
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    e.currentTarget.classList.add('active');

    // Show target category
    document.querySelectorAll('.service-category').forEach(category => {
        category.classList.remove('active');
    });

    const targetCategory = document.getElementById(targetId);
    if (targetCategory) {
        targetCategory.classList.add('active', 'fade-in');
    }
}

// Service Selection
function handleServiceChange(e) {
    const checkbox = e.target;
    const serviceName = checkbox.getAttribute('data-service');
    const basePrice = parseFloat(checkbox.getAttribute('data-price')) || 0;
    const isCustom = checkbox.hasAttribute('data-custom');
    
    if (checkbox.checked) {
        let finalPrice = basePrice;
        
        // Handle custom pricing
        if (isCustom) {
            finalPrice = calculateCustomPrice(checkbox);
        }
        
        const service = {
            name: serviceName,
            price: finalPrice,
            basePrice: basePrice,
            isCustom: isCustom,
            element: checkbox
        };
        
        selectedServices.push(service);
        
        // Show custom input if needed
        showCustomInput(checkbox);
    } else {
        // Remove service from selection
        selectedServices = selectedServices.filter(service => service.name !== serviceName);
        
        // Hide custom input
        hideCustomInput(checkbox);
    }
    
    updateCalculation();
    updateSelectedServicesList();
}

function calculateCustomPrice(checkbox) {
    const serviceItem = checkbox.closest('.service-item');
    const customInput = serviceItem.querySelector('.custom-input input');
    const multiplier = parseFloat(customInput.getAttribute('data-multiplier'));
    const additional = parseFloat(customInput.getAttribute('data-additional'));
    const basePrice = parseFloat(checkbox.getAttribute('data-price')) || 0;
    
    if (customInput && customInput.value) {
        const inputValue = parseFloat(customInput.value) || 0;
        
        if (multiplier) {
            return inputValue * multiplier;
        } else if (additional) {
            return inputValue + additional;
        }
    }
    
    return basePrice;
}

function handleCustomInputChange(e) {
    const input = e.target;
    if (input.closest('.custom-input')) {
        const serviceItem = input.closest('.service-item');
        const checkbox = serviceItem.querySelector('input[type="checkbox"]');
        
        if (checkbox && checkbox.checked) {
            // Update the service price
            const serviceName = checkbox.getAttribute('data-service');
            const newPrice = calculateCustomPrice(checkbox);
            
            // Update in selectedServices array
            const serviceIndex = selectedServices.findIndex(service => service.name === serviceName);
            if (serviceIndex !== -1) {
                selectedServices[serviceIndex].price = newPrice;
                updateCalculation();
                updateSelectedServicesList();
            }
        }
    }
}

function showCustomInput(checkbox) {
    const serviceItem = checkbox.closest('.service-item');
    const customInput = serviceItem.querySelector('.custom-input');
    if (customInput) {
        customInput.style.display = 'block';
        customInput.classList.add('slide-in');
    }
}

function hideCustomInput(checkbox) {
    const serviceItem = checkbox.closest('.service-item');
    const customInput = serviceItem.querySelector('.custom-input');
    if (customInput) {
        customInput.style.display = 'none';
        customInput.classList.remove('slide-in');
        // Reset input value
        const input = customInput.querySelector('input');
        if (input) {
            input.value = '';
        }
    }
}

// Discount handling
function handleDiscountChange(e) {
    currentDiscount = parseInt(e.target.value);
    updateCalculation();
}

// Calculation
function updateCalculation() {
    const subtotal = selectedServices.reduce((sum, service) => sum + service.price, 0);

    // Calculate discount
    const discountAmount = subtotal * (currentDiscount / 100);
    const subtotalAfterDiscount = subtotal - discountAmount;

    // Calculate VAT on discounted amount
    const vat = subtotalAfterDiscount * 0.20; // 20% DPH
    const total = subtotalAfterDiscount + vat;

    // Update display
    if (subtotalElement) subtotalElement.textContent = formatPrice(subtotal);

    // Show/hide discount row
    if (discountRowElement) {
        if (currentDiscount > 0) {
            discountRowElement.style.display = 'flex';
            if (discountPercentElement) discountPercentElement.textContent = currentDiscount;
            if (discountAmountElement) discountAmountElement.textContent = '-' + formatPrice(discountAmount);
        } else {
            discountRowElement.style.display = 'none';
        }
    }

    if (subtotalAfterDiscountElement) subtotalAfterDiscountElement.textContent = formatPrice(subtotalAfterDiscount);
    if (vatElement) vatElement.textContent = formatPrice(vat);
    if (totalElement) totalElement.textContent = formatPrice(total);

    // Enable/disable PDF button
    if (generatePDFButton) {
        const hasServices = selectedServices.length > 0;
        const hasCustomerData = validateCustomerData();
        generatePDFButton.disabled = !hasServices || !hasCustomerData;
    }
}

function updateSelectedServicesList() {
    if (!selectedServicesList) return;

    if (selectedServices.length === 0) {
        selectedServicesList.innerHTML = '<p class="no-services">Žiadne služby nie sú vybrané</p>';
        return;
    }
    
    const servicesHTML = selectedServices.map(service => `
        <div class="selected-service">
            <span class="service-name-calc">${service.name}</span>
            <span class="service-price-calc">${formatPrice(service.price)}</span>
        </div>
    `).join('');
    
    selectedServicesList.innerHTML = servicesHTML;
}

function formatPrice(price) {
    return new Intl.NumberFormat('sk-SK', {
        style: 'currency',
        currency: 'EUR',
        minimumFractionDigits: 2
    }).format(price);
}

// Customer Data
function updateCustomerData() {
    customerData = {
        name: document.getElementById('customerName').value,
        phone: document.getElementById('customerPhone').value,
        email: document.getElementById('customerEmail').value,
        address: document.getElementById('customerAddress').value,
        cemetery: document.getElementById('cemetery').value
    };
    
    updateCalculation();
}

function validateCustomerData() {
    return customerData.name && 
           customerData.phone && 
           customerData.email && 
           isValidEmail(customerData.email);
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Function to get all available services from the quote form
function getAllAvailableServices() {
    const services = [];
    const serviceCheckboxes = document.querySelectorAll('input[type="checkbox"][data-service]');

    serviceCheckboxes.forEach(checkbox => {
        const serviceName = checkbox.getAttribute('data-service');
        const basePrice = parseFloat(checkbox.getAttribute('data-price')) || 0;
        const isCustom = checkbox.hasAttribute('data-custom');

        services.push({
            id: serviceName.toLowerCase().replace(/[^a-z0-9]/g, '_'),
            name: serviceName,
            price: basePrice,
            isCustom: isCustom,
            category: getServiceCategory(checkbox)
        });
    });

    return services;
}

// Function to get service category based on its location in DOM
function getServiceCategory(checkbox) {
    const serviceCategory = checkbox.closest('.service-category');
    if (serviceCategory) {
        const categoryId = serviceCategory.id;
        switch(categoryId) {
            case 'basic-services': return 'basic';
            case 'packages': return 'package';
            case 'digital': return 'digital';
            case 'additional': return 'additional';
            case 'special': return 'special';
            default: return 'other';
        }
    }
    return 'other';
}

// Function to get currently selected services (for creating orders/invoices)
function getSelectedServicesData() {
    return selectedServices.map(service => ({
        id: service.name.toLowerCase().replace(/[^a-z0-9]/g, '_'),
        name: service.name,
        price: service.price,
        quantity: 1,
        total: service.price,
        isCustom: service.isCustom || false
    }));
}

// Function to create order from current quote
function createOrderFromQuote() {
    if (selectedServices.length === 0) {
        alert('Prosím vyberte aspoň jednu službu.');
        return;
    }

    if (!validateCustomerData()) {
        alert('Prosím vyplňte všetky povinné údaje o zákazníkovi.');
        return;
    }

    // Get customer data
    const customerName = document.getElementById('customerName').value;
    const customerPhone = document.getElementById('customerPhone').value;
    const customerEmail = document.getElementById('customerEmail').value;
    const customerAddress = document.getElementById('customerAddress').value;
    const cemetery = document.getElementById('cemetery').value;

    // Prepare quote data
    const quoteData = {
        customer: {
            name: customerName,
            phone: customerPhone,
            email: customerEmail,
            address: customerAddress,
            cemetery: cemetery
        },
        services: getSelectedServicesData(),
        discount: currentDiscount,
        subtotal: selectedServices.reduce((sum, service) => sum + service.price, 0),
        total: calculateTotal()
    };

    // Switch to orders tab and create order
    switchTab('orders');

    // Call order creation function if available
    if (typeof createOrderFromQuoteData === 'function') {
        createOrderFromQuoteData(quoteData);
    } else {
        alert('Systém objednávok nie je dostupný. Objednávku vytvorte manuálne v sekcii Objednávky.');
    }
}

function calculateTotal() {
    const subtotal = selectedServices.reduce((sum, service) => sum + service.price, 0);
    const discountAmount = subtotal * (currentDiscount / 100);
    const subtotalAfterDiscount = subtotal - discountAmount;
    const vat = subtotalAfterDiscount * 0.2;
    return subtotalAfterDiscount + vat;
}

// PDF Generation with html2pdf for perfect Unicode support
async function generatePDF() {
    if (selectedServices.length === 0) {
        alert('Prosím vyberte aspoň jednu službu.');
        return;
    }

    if (!validateCustomerData()) {
        alert('Prosím vyplňte všetky povinné údaje o zákazníkovi.');
        return;
    }

    // Show loading state
    if (generatePDFButton) {
        generatePDFButton.classList.add('loading');
        generatePDFButton.disabled = true;
        generatePDFButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generujem PDF...';
    }

    try {
        // Check if html2pdf is available
        if (typeof window.html2pdf === 'undefined') {
            throw new Error('html2pdf knižnica nie je dostupná');
        }

        // Load the PDF template
        console.log('Loading PDF template...');
        const templateResponse = await fetch('./pdf-template.html');
        console.log('Template response status:', templateResponse.status);
        if (!templateResponse.ok) {
            throw new Error(`Nepodarilo sa načítať PDF template: ${templateResponse.status} ${templateResponse.statusText}`);
        }

        const templateHTML = await templateResponse.text();
        console.log('Template loaded successfully, length:', templateHTML.length);

        // Create a temporary container
        console.log('Creating temporary container...');
        const tempContainer = document.createElement('div');
        tempContainer.innerHTML = templateHTML;
        tempContainer.style.position = 'absolute';
        tempContainer.style.left = '-9999px';
        tempContainer.style.top = '-9999px';
        document.body.appendChild(tempContainer);
        console.log('Temporary container created and added to DOM');

        // Fill in customer data
        const customerNameEl = tempContainer.querySelector('#customer-name');
        const customerPhoneEl = tempContainer.querySelector('#customer-phone');
        const customerEmailEl = tempContainer.querySelector('#customer-email');
        const customerAddressEl = tempContainer.querySelector('#customer-address');

        if (customerNameEl) customerNameEl.textContent = customerData.name || '-';
        if (customerPhoneEl) customerPhoneEl.textContent = customerData.phone || '-';
        if (customerEmailEl) customerEmailEl.textContent = customerData.email || '-';

        // Combine address and cemetery
        let fullAddress = '';
        if (customerData.address) fullAddress += customerData.address;
        if (customerData.cemetery) {
            if (fullAddress) fullAddress += ', ';
            fullAddress += `Cintorín: ${customerData.cemetery}`;
        }
        if (customerAddressEl) customerAddressEl.textContent = fullAddress || '-';
        // Fill in services
        const servicesListEl = tempContainer.querySelector('#services-list');
        if (servicesListEl) {
            servicesListEl.innerHTML = '';
            selectedServices.forEach((service, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="service-name">${service.name}</td>
                    <td style="text-align: center;">1</td>
                    <td class="service-price">${formatPrice(service.price)}</td>
                `;
                servicesListEl.appendChild(row);
            });
        }
        // Calculate and fill in totals
        const subtotal = selectedServices.reduce((sum, service) => sum + service.price, 0);

        // Calculate discount
        const discountAmount = subtotal * (currentDiscount / 100);
        const subtotalAfterDiscount = subtotal - discountAmount;

        // Calculate VAT on discounted amount
        const vat = subtotalAfterDiscount * 0.20;
        const total = subtotalAfterDiscount + vat;

        const subtotalEl = tempContainer.querySelector('#subtotal');
        const vatEl = tempContainer.querySelector('#vat');
        const totalEl = tempContainer.querySelector('#total');
        const issueDateEl = tempContainer.querySelector('#issue-date');

        // Discount elements
        const discountRowPdfEl = tempContainer.querySelector('#discountRowPdf');
        const discountPercentPdfEl = tempContainer.querySelector('#discountPercentPdf');
        const discountAmountPdfEl = tempContainer.querySelector('#discountAmountPdf');
        const subtotalAfterDiscountPdfEl = tempContainer.querySelector('#subtotalAfterDiscountPdf');

        if (subtotalEl) subtotalEl.textContent = formatPrice(subtotal);

        // Show/hide discount row in PDF
        if (discountRowPdfEl) {
            if (currentDiscount > 0) {
                discountRowPdfEl.style.display = 'table-row';
                if (discountPercentPdfEl) discountPercentPdfEl.textContent = currentDiscount;
                if (discountAmountPdfEl) discountAmountPdfEl.textContent = '-' + formatPrice(discountAmount);
            } else {
                discountRowPdfEl.style.display = 'none';
            }
        }

        if (subtotalAfterDiscountPdfEl) subtotalAfterDiscountPdfEl.textContent = formatPrice(subtotalAfterDiscount);
        if (vatEl) vatEl.textContent = formatPrice(vat);
        if (totalEl) totalEl.textContent = formatPrice(total);
        if (issueDateEl) issueDateEl.textContent = new Date().toLocaleDateString('sk-SK');

        // Get the PDF content element
        console.log('Looking for PDF content element...');
        const pdfContent = tempContainer.querySelector('#pdf-content');
        if (!pdfContent) {
            console.error('PDF content element not found in template');
            throw new Error('PDF template element not found');
        }
        console.log('PDF content element found:', pdfContent);

        // Configure html2pdf options
        const options = {
            margin: [10, 10, 10, 10],
            filename: `cenova-ponuka-${customerData.name.replace(/\s+/g, '-').toLowerCase()}-${new Date().toISOString().split('T')[0]}.pdf`,
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                letterRendering: true
            },
            jsPDF: {
                unit: 'mm',
                format: 'a4',
                orientation: 'portrait',
                compress: true
            }
        };

        console.log('Starting PDF generation with options:', options);
        // Generate and save PDF
        await html2pdf().set(options).from(pdfContent).save();
        console.log('PDF generated successfully');

        // Clean up
        document.body.removeChild(tempContainer);

    } catch (error) {
        console.error('Error generating PDF:', error);
        let errorMessage = 'Nastala chyba pri generovaní PDF.';

        if (error.message.includes('html2pdf')) {
            errorMessage = 'html2pdf knižnica nie je dostupná. Skúste obnoviť aplikáciu.';
        } else if (error.message.includes('template')) {
            errorMessage = 'Nepodarilo sa načítať PDF template.';
        } else if (error.message.includes('save')) {
            errorMessage = 'Chyba pri ukladaní PDF súboru.';
        }

        alert(errorMessage + '\n\nDetail chyby: ' + error.message);

        // Clean up temp container if it exists
        const tempContainer = document.querySelector('div[style*="-9999px"]');
        if (tempContainer) {
            document.body.removeChild(tempContainer);
        }
    } finally {
        // Reset button state
        if (generatePDFButton) {
            generatePDFButton.classList.remove('loading');
            generatePDFButton.disabled = false;
            generatePDFButton.innerHTML = '<i class="fas fa-file-pdf"></i> Generovať PDF ponuku';
        }
        updateCalculation(); // This will re-enable the button if conditions are met
    }
}

// Export functions for global access (for CRM integration)
window.getAllAvailableServices = getAllAvailableServices;
window.getSelectedServicesData = getSelectedServicesData;
window.createOrderFromQuote = createOrderFromQuote;

// Mobile Navigation Enhancement
function initializeMobileNavigation() {
    const isMobile = window.innerWidth <= 768;
    const bottomNav = document.querySelector('.pwa-bottom-nav');
    const mainNav = document.querySelector('.main-nav');

    if (isMobile && bottomNav) {
        // Show bottom navigation on mobile
        bottomNav.style.display = 'flex';

        // Hide main navigation on mobile
        if (mainNav) {
            mainNav.style.display = 'none';
        }

        // Add click handlers for bottom navigation
        const bottomNavItems = bottomNav.querySelectorAll('.nav-item');
        bottomNavItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const tabName = item.getAttribute('data-tab');

                // Update active state in bottom nav
                bottomNavItems.forEach(navItem => navItem.classList.remove('active'));
                item.classList.add('active');

                // Trigger tab change
                if (tabName && typeof switchTab === 'function') {
                    switchTab(tabName);
                } else if (tabName) {
                    // Fallback tab switching if switchTab is not available
                    const mainNavTabs = document.querySelectorAll('.nav-tab');
                    mainNavTabs.forEach(tab => {
                        tab.classList.remove('active');
                        if (tab.getAttribute('data-tab') === tabName) {
                            tab.classList.add('active');
                        }
                    });

                    // Update tab panes
                    document.querySelectorAll('.tab-pane').forEach(pane => {
                        pane.classList.remove('active');
                    });
                    const activePane = document.getElementById(`${tabName}-tab`);
                    if (activePane) {
                        activePane.classList.add('active');
                    }
                }
            });
        });
    } else if (bottomNav) {
        // Hide bottom navigation on desktop
        bottomNav.style.display = 'none';

        // Show main navigation on desktop
        if (mainNav) {
            mainNav.style.display = 'flex';
        }
    }
}

// Enhanced mobile detection and responsive behavior
function handleResponsiveChanges() {
    const isMobile = window.innerWidth <= 768;
    const isSmallMobile = window.innerWidth <= 480;
    const body = document.body;

    if (isMobile) {
        body.classList.add('mobile-view');
        body.classList.remove('desktop-view');

        // Add PWA mode class for mobile-specific styles
        if (window.matchMedia('(display-mode: standalone)').matches ||
            window.navigator.standalone === true) {
            body.classList.add('pwa-mode');
        }

        // Enable mobile optimizations
        enableMobileOptimizations();

        // Add small mobile class for extra small screens
        if (isSmallMobile) {
            body.classList.add('small-mobile');
        } else {
            body.classList.remove('small-mobile');
        }

        // Optimize service grids for mobile
        optimizeServiceGridsForMobile();

    } else {
        body.classList.add('desktop-view');
        body.classList.remove('mobile-view', 'pwa-mode', 'small-mobile');
    }

    // Reinitialize mobile navigation
    initializeMobileNavigation();
}

function optimizeServiceGridsForMobile() {
    const servicesGrids = document.querySelectorAll('.services-grid');
    servicesGrids.forEach(grid => {
        if (window.innerWidth <= 768) {
            grid.style.gridTemplateColumns = '1fr';
            grid.style.gap = '1rem';
        } else {
            grid.style.gridTemplateColumns = '';
            grid.style.gap = '';
        }
    });

    // Optimize service labels for touch
    const serviceLabels = document.querySelectorAll('.service-label');
    serviceLabels.forEach(label => {
        if (window.innerWidth <= 768) {
            label.style.minHeight = '60px';
            label.style.padding = '1rem';
        }
    });

    // Optimize quotes layout for mobile
    optimizeQuotesLayoutForMobile();
}

function optimizeQuotesLayoutForMobile() {
    const quotesLayout = document.querySelector('.quotes-layout');
    if (!quotesLayout) return;

    if (window.innerWidth <= 768) {
        // Force mobile layout
        quotesLayout.style.display = 'flex';
        quotesLayout.style.flexDirection = 'column';
        quotesLayout.style.gridTemplateColumns = 'none';
        quotesLayout.style.padding = '0.5rem';
        quotesLayout.style.gap = '1rem';

        // Ensure form-area is visible
        const formArea = document.querySelector('.form-area');
        if (formArea) {
            formArea.style.display = 'block';
            formArea.style.visibility = 'visible';
            formArea.style.width = '100%';
            formArea.style.padding = '1rem';
            formArea.style.background = 'white';
            formArea.style.borderRadius = '8px';
            formArea.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
        }

        // Ensure customer section is visible
        const customerSection = document.querySelector('.customer-section');
        if (customerSection) {
            customerSection.style.display = 'block';
            customerSection.style.visibility = 'visible';
        }

        // Ensure services section is visible
        const servicesSection = document.querySelector('.services-section');
        if (servicesSection) {
            servicesSection.style.display = 'block';
            servicesSection.style.visibility = 'visible';
        }

        // Optimize sidebar navigation
        const serviceNav = document.querySelector('.service-nav');
        if (serviceNav) {
            const navLinks = serviceNav.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all links
                    navLinks.forEach(l => l.classList.remove('active'));

                    // Add active class to clicked link
                    this.classList.add('active');

                    // Show corresponding service category
                    const targetId = this.getAttribute('href').substring(1);
                    showServiceCategory(targetId);

                    // Scroll to service selection on mobile
                    const serviceSelection = document.querySelector('.service-selection');
                    if (serviceSelection) {
                        serviceSelection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }
                });
            });
        }

        // Optimize form sections
        const formSections = document.querySelectorAll('.customer-section, .service-selection, .summary-area');
        formSections.forEach(section => {
            section.style.marginBottom = '1.5rem';
            section.style.padding = '1rem';
        });

    } else {
        // Reset to desktop layout
        quotesLayout.style.display = '';
        quotesLayout.style.gridTemplateColumns = '';
        quotesLayout.style.padding = '';
    }
}

// Touch-friendly enhancements for mobile
function initializeTouchEnhancements() {
    if ('ontouchstart' in window) {
        document.body.classList.add('touch-device');

        // Add touch feedback to buttons
        const buttons = document.querySelectorAll('button, .btn, .nav-tab, .nav-item');
        buttons.forEach(button => {
            button.addEventListener('touchstart', function() {
                this.classList.add('touch-active');
            });

            button.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.classList.remove('touch-active');
                }, 150);
            });
        });
    }
}

// Initialize mobile features when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize responsive behavior
    handleResponsiveChanges();

    // Initialize touch enhancements
    initializeTouchEnhancements();

    // Listen for window resize
    window.addEventListener('resize', handleResponsiveChanges);

    // Listen for orientation change on mobile
    window.addEventListener('orientationchange', function() {
        setTimeout(handleResponsiveChanges, 100);
    });

    // Force mobile layout optimization after a short delay
    setTimeout(() => {
        if (window.innerWidth <= 768) {
            optimizeQuotesLayoutForMobile();
            console.log('Mobile layout optimization applied');
        }
    }, 500);
});

// PWA specific mobile enhancements
if ('serviceWorker' in navigator) {
    // Add PWA install prompt handling
    let deferredPrompt;

    window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;

        // Show install button on mobile
        if (window.innerWidth <= 768) {
            showPWAInstallPrompt();
        }
    });

    function showPWAInstallPrompt() {
        const installButton = document.createElement('button');
        installButton.className = 'pwa-install-button';
        installButton.innerHTML = '<i class="fas fa-download"></i> Nainštalovať aplikáciu';
        installButton.style.cssText = `
            position: fixed;
            bottom: 80px;
            right: 20px;
            background: #5e2e60;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 25px;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 1001;
            display: flex;
            align-items: center;
            gap: 8px;
        `;

        installButton.addEventListener('click', async () => {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;

                if (outcome === 'accepted') {
                    console.log('PWA installed');
                }

                deferredPrompt = null;
                installButton.remove();
            }
        });

        document.body.appendChild(installButton);

        // Auto-hide after 10 seconds
        setTimeout(() => {
            if (installButton.parentNode) {
                installButton.remove();
            }
        }, 10000);
    }
}




