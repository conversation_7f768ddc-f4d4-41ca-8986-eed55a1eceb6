<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cenová ponuka - eSpomienka</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: white;
        }
        
        .pdf-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }
        
        .header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #8B5CF6;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            margin-right: 20px;
        }
        
        .company-info h1 {
            font-size: 24px;
            font-weight: 700;
            color: #8B5CF6;
            margin-bottom: 5px;
        }
        
        .company-info .tagline {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .company-info .contact {
            font-size: 11px;
            color: #888;
        }
        
        .quote-title {
            text-align: center;
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 30px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .customer-section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #8B5CF6;
            margin-bottom: 15px;
            border-bottom: 1px solid #E5E7EB;
            padding-bottom: 5px;
        }
        
        .customer-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .customer-field {
            display: flex;
            flex-direction: column;
        }
        
        .customer-field.full-width {
            grid-column: 1 / -1;
        }
        
        .field-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 3px;
            font-size: 11px;
        }
        
        .field-value {
            color: #111827;
            font-weight: 400;
            padding: 5px 0;
            border-bottom: 1px solid #E5E7EB;
        }
        
        .services-section {
            margin-bottom: 30px;
        }
        
        .services-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .services-table th {
            background-color: #F3F4F6;
            color: #374151;
            font-weight: 600;
            padding: 12px 8px;
            text-align: left;
            border: 1px solid #D1D5DB;
            font-size: 11px;
        }
        
        .services-table td {
            padding: 10px 8px;
            border: 1px solid #D1D5DB;
            font-size: 11px;
        }
        
        .services-table tr:nth-child(even) {
            background-color: #F9FAFB;
        }
        
        .price-cell {
            text-align: right;
            font-weight: 500;
        }
        
        .summary-section {
            margin-top: 30px;
            display: flex;
            justify-content: flex-end;
        }
        
        .summary-table {
            width: 300px;
            border-collapse: collapse;
        }
        
        .summary-table td {
            padding: 8px 12px;
            border: 1px solid #D1D5DB;
            font-size: 12px;
        }
        
        .summary-table .label {
            background-color: #F3F4F6;
            font-weight: 500;
            color: #374151;
        }
        
        .summary-table .value {
            text-align: right;
            font-weight: 500;
        }
        
        .summary-table .total {
            background-color: #8B5CF6;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }
        
        .discount-row {
            color: #DC2626;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #E5E7EB;
            text-align: center;
            font-size: 10px;
            color: #6B7280;
        }
        
        .footer p {
            margin-bottom: 5px;
        }
        
        .date-info {
            text-align: right;
            margin-bottom: 20px;
            font-size: 11px;
            color: #6B7280;
        }
        
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .pdf-container {
                max-width: none;
                margin: 0;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div id="pdf-content" class="pdf-container">
        <!-- Header -->
        <div class="header">
            <div class="logo-placeholder">
                <div style="width: 60px; height: 60px; background: #8B5CF6; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 18px;">eS</div>
            </div>
            <div class="company-info">
                <h1>eSpomienka</h1>
                <div class="tagline">Starostlivosť o hrobové miesta</div>
                <div class="contact">
                    Tel: +*********** 464 | Email: <EMAIL><br>
                    www.espomienka.sk
                </div>
            </div>
        </div>
        
        <!-- Quote Title -->
        <div class="quote-title">Cenová ponuka</div>
        
        <!-- Date -->
        <div class="date-info">
            Dátum vytvorenia: <span id="issue-date"></span>
        </div>

        <!-- Customer Information -->
        <div class="customer-section">
            <div class="section-title">Údaje o zákazníkovi</div>
            <div class="customer-grid">
                <div class="customer-field">
                    <div class="field-label">Meno a priezvisko:</div>
                    <div class="field-value" id="customer-name"></div>
                </div>
                <div class="customer-field">
                    <div class="field-label">Telefón:</div>
                    <div class="field-value" id="customer-phone"></div>
                </div>
                <div class="customer-field">
                    <div class="field-label">Email:</div>
                    <div class="field-value" id="customer-email"></div>
                </div>
                <div class="customer-field">
                    <div class="field-label">Adresa:</div>
                    <div class="field-value" id="customer-address"></div>
                </div>
                <div class="customer-field full-width">
                    <div class="field-label">Cintorín/Lokalita:</div>
                    <div class="field-value" id="customer-cemetery"></div>
                </div>
            </div>
        </div>
        
        <!-- Services -->
        <div class="services-section">
            <div class="section-title">Vybrané služby</div>
            <table class="services-table">
                <thead>
                    <tr>
                        <th style="width: 70%;">Názov služby</th>
                        <th style="width: 15%;">Množstvo</th>
                        <th style="width: 15%;">Cena</th>
                    </tr>
                </thead>
                <tbody id="services-list">
                    <!-- Services will be populated by JavaScript -->
                </tbody>
            </table>
        </div>
        
        <!-- Price Summary -->
        <div class="summary-section">
            <table class="summary-table">
                <tr>
                    <td class="label">Súčet bez DPH:</td>
                    <td class="value" id="subtotal">0,00 EUR</td>
                </tr>
                <tr id="discountRowPdf" style="display: none;">
                    <td class="label discount-row">Zľava (<span id="discountPercentPdf">0</span>%):</td>
                    <td class="value discount-row" id="discountAmountPdf">-0,00 EUR</td>
                </tr>
                <tr>
                    <td class="label">Súčet po zľave:</td>
                    <td class="value" id="subtotalAfterDiscountPdf">0,00 EUR</td>
                </tr>
                <tr>
                    <td class="label">DPH 20%:</td>
                    <td class="value" id="vat">0,00 EUR</td>
                </tr>
                <tr class="total">
                    <td class="label">Celkom s DPH:</td>
                    <td class="value" id="total">0,00 EUR</td>
                </tr>
            </table>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p><strong>Platnosť ponuky:</strong> 30 dní od dátumu vytvorenia</p>
            <p><strong>Spôsob platby:</strong> Bankový prevod alebo hotovosť</p>
            <p>Ďakujeme za Váš záujem o naše služby!</p>
        </div>
    </div>
</body>
</html>
